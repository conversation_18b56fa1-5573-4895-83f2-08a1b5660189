{"name": "animagen", "version": "1.0.0", "description": "Professional animation creation tool", "main": "backend/index.js", "scripts": {"start": "node backend/index.js", "build": "echo 'Building with <PERSON>er - see Dockerfile'", "heroku-postbuild": "cd backend && npm install && cd ../frontend && npm install && npm run build && mkdir -p ../backend/public && cp -r dist/* ../backend/public/", "dev": "concurrently \"cd backend && npm run dev\" \"cd frontend && npm run dev\"", "test": "cd backend && npm test", "test:browser": "node tests/browser-mcp/run-tests.js", "test:ui": "npm run test:browser", "test:imageupload": "node tests/browser-mcp/run-tests.js", "test:watch": "node tests/browser-mcp/watch-tests.js", "test:setup": "node tests/browser-mcp/setup-check.js", "test:workflow": "node tests/browser-mcp/complete-workflow.test.js"}, "engines": {"node": "18.x"}, "buildpacks": [{"url": "hero<PERSON>/nodejs"}], "author": "GsusFC", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}
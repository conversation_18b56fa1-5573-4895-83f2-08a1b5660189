const express = require('express');
const router = express.Router();
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const { promisify } = require('util');
const execAsync = promisify(require('child_process').exec);
const { JobTypes } = require('../queues/jobTypes');
const { addJob } = require('../queues/videoQueue');

// Helper para resolución automática (placeholder, puedes mejorar)
async function calculateAutoResolution(images, sessionId, maxDimension = 1920) {
  return { width: maxDimension, height: maxDimension };
}

// Helper function to safely resolve file paths (Node.js Best Practice)
function resolveImagePath(sessionId, filename) {
  // Sanitize inputs to prevent directory traversal attacks
  const sanitizedSessionId = sessionId.replace(/[^a-zA-Z0-9_-]/g, '');
  const sanitizedFilename = filename.replace(/[^a-zA-Z0-9._-]/g, '');

  // Define possible directories where images might be stored
  const possibleDirs = [
    path.join(__dirname, '..', 'temp', sanitizedSessionId),
    path.join(__dirname, '..', 'uploads', sanitizedSessionId),
    path.join(__dirname, '..', 'uploads', 'temp', sanitizedSessionId)
  ];

  // Try to find the file in each directory
  for (const dir of possibleDirs) {
    const fullPath = path.join(dir, sanitizedFilename);
    if (fs.existsSync(fullPath)) {
      console.log(`🔍 Found image: ${sanitizedFilename} in ${dir}`);
      return fullPath;
    }
  }

  console.log(`❌ Image not found: ${sanitizedFilename} in any directory`);
  return null;
}

router.post('/:format', async (req, res) => {
  try {
    console.log('🎬 [UNIFIED EXPORT] Request received:', JSON.stringify(req.body, null, 2));
    const format = req.params.format.toLowerCase();
    const supportedFormats = ['mp4', 'webm', 'mov', 'gif'];
    if (!supportedFormats.includes(format)) {
      return res.status(400).json({
        success: false,
        error: `Unsupported format: ${format}. Supported: ${supportedFormats.join(', ')}`
      });
    }
    const {
      images,
      transitions = [],
      frameDurations = [],
      sessionId,
      fps = format === 'gif' ? 15 : 30,
      quality = 'standard'
    } = req.body;
    console.log(`🎬 [UNIFIED EXPORT] Request for ${format}, images: ${images?.length}, sessionId: ${sessionId}`);
    if (!Array.isArray(images) || images.length === 0) {
      return res.status(400).json({ success: false, error: 'No images provided' });
    }
    if (!sessionId) {
      return res.status(400).json({ success: false, error: 'Session ID is required' });
    }
    // Procesamiento directo para <=3 imágenes
    if (images.length <= 3 && process.env.ENABLE_DIRECT_PROCESSING !== 'false') {
      try {
        const outputDir = path.join(__dirname, '../output');
        if (!fs.existsSync(outputDir)) fs.mkdirSync(outputDir, { recursive: true });
        const jobId = `direct_${Date.now()}_${uuidv4().substring(0, 8)}`;
        const outputFile = path.join(outputDir, `unified_${jobId}.${format}`);
        const inputFlags = [];
        const filterParts = [];
        const targetResolution = await calculateAutoResolution(images, sessionId, format === 'gif' ? 720 : 1920);
        images.forEach((img, index) => {
          const imagePath = resolveImagePath(sessionId, img.filename);
          if (!imagePath) {
            throw new Error(`Image not found: ${img.filename}`);
          }
          const duration = (frameDurations[index] || 1000) / 1000;
          inputFlags.push(`-loop 1 -t ${duration} -i "${imagePath}"`);
        });
        const scaleFilters = images.map((_, index) =>
          `[${index}:v]scale=${targetResolution.width}:${targetResolution.height}:force_original_aspect_ratio=decrease,pad=${targetResolution.width}:${targetResolution.height}:(ow-iw)/2:(oh-ih)/2[v${index}]`
        );
        let filterComplex;
        let outputOptions;
        if (format === 'gif') {
          const concatInputs = images.map((_, index) => `[v${index}]`).join('');
          filterComplex = `${scaleFilters.join(';')};${concatInputs}concat=n=${images.length}:v=1:a=0[out];[out]split[s0][s1];[s0]palettegen[p];[s1][p]paletteuse[outgif]`;
          outputOptions = `-map "[outgif]" -r ${fps} -loop 0`;
        } else {
          if (transitions.some(t => t && t.type && t.type !== 'cut' && t.type !== 'none')) {
            filterComplex = `${scaleFilters.join(';')};`;
            let lastOutput = '[v0]';
            let timePosition = (frameDurations[0] || 1000) / 1000;
            for (let i = 0; i < images.length - 1; i++) {
              const trans = transitions[i] || { type: 'fade', duration: 500 };
              const transType = trans.type === 'cut' || trans.type === 'none' ? 'fade' : trans.type;
              const transDuration = Math.max((trans.duration || 500) / 1000, 0.1);
              const offset = timePosition - transDuration;
              const nextOutput = i === images.length - 2 ? '[out]' : `[t${i}]`;
              filterComplex += `${lastOutput}[v${i+1}]xfade=transition=${transType}:duration=${transDuration}:offset=${offset}${nextOutput};`;
              lastOutput = nextOutput;
              timePosition += (frameDurations[i+1] || 1000) / 1000;
            }
            filterComplex = filterComplex.slice(0, -1);
          } else {
            const concatInputs = images.map((_, index) => `[v${index}]`).join('');
            filterComplex = `${scaleFilters.join(';')};${concatInputs}concat=n=${images.length}:v=1:a=0[out]`;
          }
          if (format === 'mp4' || format === 'mov') {
            outputOptions = `-map "[out]" -c:v libx264 -preset fast -crf 23 -pix_fmt yuv420p -movflags +faststart -r ${fps}`;
          } else if (format === 'webm') {
            outputOptions = `-map "[out]" -c:v libvpx-vp9 -crf 23 -b:v 0 -pix_fmt yuv420p -r ${fps}`;
          }
        }
        const ffmpegCmd = `ffmpeg ${inputFlags.join(' ')} -filter_complex "${filterComplex}" ${outputOptions} -y "${outputFile}"`;
        console.log(`🎬 Direct FFmpeg processing command:`, ffmpegCmd);
        await execAsync(ffmpegCmd);
        return res.json({
          success: true,
          jobId: jobId,
          status: 'completed',
          message: `${format.toUpperCase()} created successfully (direct processing)`,
          downloadUrl: `/api/export/download/${jobId}`,
          isDirect: true
        });
      } catch (ffmpegError) {
        console.error(`❌ Direct ${format} processing failed:`, ffmpegError);
        console.log('Falling back to queue processing...');
      }
    }
    // Encola el trabajo para el worker
    const jobData = {
      images,
      transitions,
      frameDurations,
      sessionId,
      format,
      fps,
      quality
    };
    const job = await addJob('unified_export', jobData);
    res.json({
      success: true,
      jobId: job.id,
      message: `${format.toUpperCase()} export job queued successfully`,
      statusUrl: `/api/export/status/${job.id}`,
      downloadUrl: `/api/export/download/${job.id}`
    });
  } catch (error) {
    console.error('❌ Failed to process unified export:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to process export',
      details: error.message
    });
  }
});

module.exports = router; 